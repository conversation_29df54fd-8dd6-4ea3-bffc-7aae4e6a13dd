import { useState, useRef, useEffect } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { 
  ArrowLeft, 
  Save, 
  FileText, 
  Plus, 
  MoreHorizontal, 
  Edit3, 
  Trash2, 
  Send, 
  Bot, 
  User,
  ChevronDown,
  ChevronRight,
  MessageSquare
} from 'lucide-react';

interface Section {
  id: string;
  title: string;
  content: string;
  level: number;
  children?: Section[];
}

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

const DocumentEditor = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { documentId } = useParams();
  const chatEndRef = useRef<HTMLDivElement>(null);

  // 从路由状态获取项目信息
  const { projectId, templateId, mode } = location.state || {};
  
  // 根据模板ID获取文档标题
  const getDocumentTitle = () => {
    if (templateId) {
      const templateTitles: { [key: string]: string } = {
        '1': '建设项目环境影响报告书',
        '2': '建设项目环境影响报告表',
        '3': '环境影响登记表',
        '4': '水土保持方案报告书',
        '5': '安全评价报告',
        '6': '职业病危害预评价',
        '7': '家居制造业环评报告',
        '8': '医院建设项目环评',
        '9': '塑料制品安全评价'
      };
      return templateTitles[templateId] || '新建文档';
    }
    return mode === 'edit' ? '编辑文档' : '新建文档';
  };

  // 文档状态
  const [documentTitle, setDocumentTitle] = useState(getDocumentTitle());
  const [sections, setSections] = useState<Section[]>([
    {
      id: '1',
      title: '1. 项目概述',
      content: '本项目位于...',
      level: 1,
      children: [
        {
          id: '1-1',
          title: '1.1 项目基本信息',
          content: '项目名称：\n建设单位：\n建设地点：',
          level: 2
        },
        {
          id: '1-2',
          title: '1.2 项目建设内容',
          content: '建设规模：\n主要建设内容：',
          level: 2
        }
      ]
    },
    {
      id: '2',
      title: '2. 环境现状调查',
      content: '根据现场调查和监测数据...',
      level: 1,
      children: [
        {
          id: '2-1',
          title: '2.1 自然环境',
          content: '地理位置：\n地形地貌：\n气候条件：',
          level: 2
        }
      ]
    },
    {
      id: '3',
      title: '3. 环境影响预测',
      content: '基于工程分析结果...',
      level: 1
    }
  ]);
  
  const [activeSection, setActiveSection] = useState<string>('1');
  const [expandedSections, setExpandedSections] = useState<string[]>(['1', '2']);
  
  // 聊天状态
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'assistant',
      content: '你好！我是你的写作助手。我可以帮助你：\n\n• 生成章节内容\n• 优化文字表达\n• 检查格式规范\n• 提供专业建议\n\n有什么需要帮助的吗？',
      timestamp: new Date()
    }
  ]);
  const [chatInput, setChatInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  
  // 编辑状态
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [editingTitleValue, setEditingTitleValue] = useState(documentTitle);

  // 获取当前激活的章节
  const getCurrentSection = (): Section | undefined => {
    const findSection = (sections: Section[]): Section | undefined => {
      for (const section of sections) {
        if (section.id === activeSection) return section;
        if (section.children) {
          const found = findSection(section.children);
          if (found) return found;
        }
      }
      return undefined;
    };
    return findSection(sections);
  };

  // 更新章节内容
  const updateSectionContent = (content: string) => {
    const updateSection = (sections: Section[]): Section[] => {
      return sections.map(section => {
        if (section.id === activeSection) {
          return { ...section, content };
        }
        if (section.children) {
          return { ...section, children: updateSection(section.children) };
        }
        return section;
      });
    };
    setSections(updateSection(sections));
  };

  // 切换章节展开状态
  const toggleSectionExpanded = (sectionId: string) => {
    setExpandedSections(prev =>
      prev.includes(sectionId)
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  // 添加新章节
  const addNewSection = () => {
    const newSection: Section = {
      id: Date.now().toString(),
      title: `${sections.length + 1}. 新章节`,
      content: '',
      level: 1
    };
    setSections(prev => [...prev, newSection]);
    setActiveSection(newSection.id);
  };

  // 添加子章节
  const addSubSection = (parentId: string) => {
    const updateSections = (sections: Section[]): Section[] => {
      return sections.map(section => {
        if (section.id === parentId) {
          const newSubSection: Section = {
            id: `${parentId}-${Date.now()}`,
            title: `${section.title.split('.')[0]}.${(section.children?.length || 0) + 1} 新子章节`,
            content: '',
            level: 2
          };
          return {
            ...section,
            children: [...(section.children || []), newSubSection]
          };
        }
        if (section.children) {
          return { ...section, children: updateSections(section.children) };
        }
        return section;
      });
    };
    setSections(updateSections(sections));
    setExpandedSections(prev => [...prev, parentId]);
  };

  // 发送聊天消息
  const sendMessage = async (customMessage?: string) => {
    const messageToSend = customMessage || chatInput;
    if (!messageToSend.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: messageToSend,
      timestamp: new Date()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setChatInput('');
    setIsTyping(true);

    // 模拟AI回复
    setTimeout(() => {
      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: generateAIResponse(messageToSend),
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, assistantMessage]);
      setIsTyping(false);
    }, 1500);
  };

  // 生成AI回复
  const generateAIResponse = (input: string): string => {
    const responses = [
      '我建议在这个章节中添加更多具体的数据和案例来支撑你的观点。',
      '这个表述很好，不过可以考虑使用更专业的术语来提升文档的权威性。',
      '建议在此处添加相关的法规依据和标准要求。',
      '内容结构清晰，建议补充一些图表来更直观地展示信息。',
      '这部分内容可以进一步细化，添加具体的实施步骤和时间安排。'
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  };

  // 保存文档
  const saveDocument = () => {
    console.log('保存文档:', { documentTitle, sections });
    // 这里应该调用API保存文档
    alert('文档已保存');
  };

  // 滚动到聊天底部
  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatMessages]);

  const currentSection = getCurrentSection();

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* 顶部工具栏 */}
      <div className="bg-white border-b border-gray-200 px-6 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => {
              if (projectId) {
                navigate(`/my-workspace/private/${projectId}`);
              } else {
                navigate('/my-workspace/private');
              }
            }}
            className="text-gray-500 hover:text-gray-700"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          
          {isEditingTitle ? (
            <input
              type="text"
              value={editingTitleValue}
              onChange={(e) => setEditingTitleValue(e.target.value)}
              onBlur={() => {
                setDocumentTitle(editingTitleValue);
                setIsEditingTitle(false);
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  setDocumentTitle(editingTitleValue);
                  setIsEditingTitle(false);
                }
              }}
              className="text-lg font-semibold text-gray-900 bg-transparent border-b border-blue-500 focus:outline-none"
              autoFocus
            />
          ) : (
            <h1 
              className="text-lg font-semibold text-gray-900 cursor-pointer hover:text-blue-600"
              onClick={() => setIsEditingTitle(true)}
            >
              {documentTitle}
            </h1>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={saveDocument}
            className="btn-primary"
          >
            <Save className="h-4 w-4 mr-2" />
            保存
          </button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧目录 */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-900">文档目录</h3>
              <button
                onClick={addNewSection}
                className="text-gray-400 hover:text-gray-600"
                title="添加新章节"
              >
                <Plus className="h-4 w-4" />
              </button>
            </div>
          </div>
          
          <div className="flex-1 overflow-y-auto p-2">
            {/* 目录树 */}
            {sections.map((section) => (
              <div key={section.id} className="mb-1">
                <div
                  className={`flex items-center px-3 py-2 rounded-lg cursor-pointer group ${
                    activeSection === section.id
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                  onClick={() => setActiveSection(section.id)}
                >
                  {section.children && section.children.length > 0 && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleSectionExpanded(section.id);
                      }}
                      className="mr-1 p-0.5 rounded hover:bg-gray-200"
                    >
                      {expandedSections.includes(section.id) ? (
                        <ChevronDown className="h-3 w-3" />
                      ) : (
                        <ChevronRight className="h-3 w-3" />
                      )}
                    </button>
                  )}
                  <FileText className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span className="text-sm font-medium truncate flex-1">
                    {section.title}
                  </span>
                  <button className="opacity-0 group-hover:opacity-100 p-1 rounded hover:bg-gray-200">
                    <MoreHorizontal className="h-3 w-3" />
                  </button>
                </div>

                {/* 子章节 */}
                {section.children && expandedSections.includes(section.id) && (
                  <div className="ml-4 mt-1">
                    {section.children.map((child) => (
                      <div
                        key={child.id}
                        className={`flex items-center px-3 py-2 rounded-lg cursor-pointer group ${
                          activeSection === child.id
                            ? 'bg-blue-50 text-blue-700'
                            : 'text-gray-600 hover:bg-gray-50'
                        }`}
                        onClick={() => setActiveSection(child.id)}
                      >
                        <div className="w-4 mr-2"></div>
                        <FileText className="h-3 w-3 mr-2 flex-shrink-0" />
                        <span className="text-sm truncate flex-1">
                          {child.title}
                        </span>
                        <button className="opacity-0 group-hover:opacity-100 p-1 rounded hover:bg-gray-200">
                          <MoreHorizontal className="h-3 w-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* 中间编辑区域 */}
        <div className="flex-1 flex flex-col">
          {/* 编辑器工具栏 */}
          <div className="bg-white border-b border-gray-200 px-6 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <span>当前章节:</span>
                <span className="font-medium text-gray-900">
                  {currentSection?.title || '请选择章节'}
                </span>
              </div>

              {currentSection && (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => {
                      const prompt = `请帮我生成"${currentSection.title}"这个章节的内容大纲`;
                      sendMessage(prompt);
                    }}
                    className="text-xs px-3 py-1 bg-blue-50 text-blue-600 rounded-full hover:bg-blue-100"
                  >
                    AI 生成大纲
                  </button>
                  <button
                    onClick={() => {
                      const prompt = `请帮我优化"${currentSection.title}"章节的内容表达：\n\n${currentSection.content}`;
                      sendMessage(prompt);
                    }}
                    className="text-xs px-3 py-1 bg-green-50 text-green-600 rounded-full hover:bg-green-100"
                  >
                    优化内容
                  </button>
                </div>
              )}
            </div>
          </div>
          
          {/* 编辑器内容 */}
          <div className="flex-1 p-6 overflow-y-auto">
            {currentSection ? (
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {currentSection.title}
                </h2>
                <textarea
                  value={currentSection.content}
                  onChange={(e) => updateSectionContent(e.target.value)}
                  className="w-full h-96 p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                  placeholder="在此输入章节内容..."
                />
              </div>
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                <div className="text-center">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>请从左侧目录选择要编辑的章节</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 右侧聊天区域 */}
        <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5 text-blue-500" />
              <h3 className="text-sm font-medium text-gray-900">AI 写作助手</h3>
            </div>
          </div>
          
          {/* 聊天消息区域 */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {chatMessages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] rounded-lg px-3 py-2 ${
                    message.type === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  <div className="flex items-start space-x-2">
                    {message.type === 'assistant' && (
                      <Bot className="h-4 w-4 mt-0.5 flex-shrink-0 text-blue-500" />
                    )}
                    <div className="flex-1">
                      <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                      <p className={`text-xs mt-1 ${
                        message.type === 'user' ? 'text-blue-100' : 'text-gray-500'
                      }`}>
                        {message.timestamp.toLocaleTimeString('zh-CN', {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                    {message.type === 'user' && (
                      <User className="h-4 w-4 mt-0.5 flex-shrink-0 text-blue-100" />
                    )}
                  </div>
                </div>
              </div>
            ))}

            {/* AI 正在输入指示器 */}
            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-gray-100 rounded-lg px-3 py-2">
                  <div className="flex items-center space-x-2">
                    <Bot className="h-4 w-4 text-blue-500" />
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div ref={chatEndRef} />
          </div>
          
          {/* 聊天输入区域 */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex space-x-2">
              <input
                type="text"
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && sendMessage()}
                placeholder="输入消息..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <button
                onClick={sendMessage}
                disabled={!chatInput.trim()}
                className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Send className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentEditor;
